// Services Orbital Timeline Animation
class ServicesOrbitalTimeline {
    constructor() {
        this.container = document.getElementById('orbitalContainerMini');
        this.serviceNodes = document.querySelectorAll('.service-node-mini');
        this.centralHub = document.querySelector('.central-hub-mini');
        this.orbitalRing = document.querySelector('.orbital-ring-mini');
        
        this.isAnimating = true;
        this.currentRotation = 0;
        this.animationSpeed = 0.5; // degrees per frame
        this.hoveredNode = null;
        this.isCardOpen = false;
        this.animationFrameId = null;
        
        this.init();
    }
    
    init() {
        if (!this.container) return;
        
        this.setupEventListeners();
        this.initializeAnimation();
        this.animateOnScroll();
    }
    
    initializeAnimation() {
        console.log('Initializing animation...');
        this.isAnimating = true;
        this.isCardOpen = false;
        this.startAnimation();
    }
    
    setupEventListeners() {
        // Service node hover events
        this.serviceNodes.forEach((node, index) => {
            node.addEventListener('mouseenter', () => this.onNodeHover(node, index));
            node.addEventListener('mouseleave', () => this.onNodeLeave(node, index));
            node.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent event bubbling
                this.onNodeClick(node, index);
            });
        });
        
        // Container pause on hover (only if no card is open)
        this.container.addEventListener('mouseenter', () => {
            if (!this.isCardOpen) {
                this.pauseAnimation();
            }
        });
        
        this.container.addEventListener('mouseleave', () => {
            if (!this.isCardOpen) {
                this.resumeAnimation();
            }
        });
        
        // Close cards when clicking outside the container
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target) && this.isCardOpen) {
                this.closeAllServiceCards();
            }
        });
        
        // Also close cards when clicking on the container but not on nodes
        this.container.addEventListener('click', (e) => {
            if (e.target === this.container && this.isCardOpen) {
                this.closeAllServiceCards();
            }
        });
        
        // Intersection Observer for entrance animation
        this.observeEntrance();
    }
    
    onNodeHover(node, index) {
        this.hoveredNode = node;
        
        // Scale up the hovered node
        gsap.to(node.querySelector('.node-icon-mini'), {
            scale: 1.3,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
        
        // Pulse effect
        gsap.to(node.querySelector('.node-icon-mini'), {
            boxShadow: "0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(185, 34, 75, 0.6)",
            duration: 0.3
        });
        
        // Highlight related nodes (for demo purposes, we'll highlight adjacent nodes)
        this.highlightRelatedNodes(index);
    }
    
    onNodeLeave(node, index) {
        this.hoveredNode = null;
        
        // Reset scale
        gsap.to(node.querySelector('.node-icon-mini'), {
            scale: 1,
            duration: 0.3,
            ease: "power2.out"
        });
        
        // Reset shadow
        gsap.to(node.querySelector('.node-icon-mini'), {
            boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
            duration: 0.3
        });
        
        // Remove highlights
        this.removeHighlights();
    }
    
    onNodeClick(node, index) {
        // Create ripple effect
        this.createRippleEffect(node);

        // Check if this node is already active
        if (node.classList.contains('active')) {
            // If already active, close it
            this.closeServiceCard(node);
            return;
        }

        // FIRST: Set card open flag to prevent animation resume
        this.isCardOpen = true;

        // THEN: Stop animation completely
        this.pauseAnimation();
        console.log('Node clicked - Animation stopped, card opened:', this.isCardOpen, 'isAnimating:', this.isAnimating);

        // Close any existing expanded cards (this won't resume animation now)
        this.closeExistingCards();

        // Show detailed service card
        this.showServiceCard(node, index);

        // Center the clicked node
        this.focusOnNode(node, index);
    }
    
    highlightRelatedNodes(currentIndex) {
        this.serviceNodes.forEach((node, index) => {
            const icon = node.querySelector('.node-icon-mini');
            if (index !== currentIndex) {
                // Adjacent nodes get subtle highlight
                const isAdjacent = Math.abs(index - currentIndex) === 1 || 
                                 (currentIndex === 0 && index === this.serviceNodes.length - 1) || 
                                 (currentIndex === this.serviceNodes.length - 1 && index === 0);
                
                if (isAdjacent) {
                    gsap.to(icon, {
                        scale: 1.1,
                        boxShadow: "0 0 10px rgba(255, 255, 255, 0.4)",
                        duration: 0.3
                    });
                } else {
                    gsap.to(icon, {
                        opacity: 0.6,
                        duration: 0.3
                    });
                }
            }
        });
    }
    
    removeHighlights() {
        this.serviceNodes.forEach((node) => {
            const icon = node.querySelector('.node-icon-mini');
            gsap.to(icon, {
                scale: 1,
                opacity: 1,
                boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
                duration: 0.3
            });
        });
    }
    
    createRippleEffect(node) {
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 5;
            transform: translate(-50%, -50%);
        `;
        
        node.appendChild(ripple);
        
        gsap.to(ripple, {
            width: 100,
            height: 100,
            opacity: 0,
            duration: 0.6,
            ease: "power2.out",
            onComplete: () => ripple.remove()
        });
    }
    
    showServiceCard(node, index) {
        const serviceDetails = node.querySelector('.service-details-mini');
        node.classList.add('active');

        // Show the service card with animation
        gsap.set(serviceDetails, {
            display: 'block',
            visibility: 'visible',
            opacity: 0,
            scale: 0.8,
            y: 20
        });

        gsap.to(serviceDetails, {
            opacity: 1,
            scale: 1,
            y: 0,
            duration: 0.5,
            ease: "back.out(1.7)"
        });

        // Add close button functionality
        this.addCloseButton(serviceDetails, node);
    }
    

    
    closeAllServiceCards() {
        // Immediately set flags to resume animation
        this.isCardOpen = false;
        console.log('Cards closed, resuming animation:', this.isCardOpen);

        this.serviceNodes.forEach(node => {
            const serviceDetails = node.querySelector('.service-details-mini');
            node.classList.remove('active');

            gsap.to(serviceDetails, {
                opacity: 0,
                scale: 0.8,
                y: 20,
                duration: 0.3,
                ease: "power2.inOut",
                onComplete: () => {
                    gsap.set(serviceDetails, {
                        display: 'none',
                        visibility: 'hidden'
                    });
                }
            });
        });

        // Resume animation immediately
        this.resumeAnimation();
    }
    
    closeExistingCards() {
        // Close cards without resuming animation (for when opening new card)
        console.log('Closing existing cards without animation resume');

        this.serviceNodes.forEach(node => {
            const serviceDetails = node.querySelector('.service-details-mini');
            node.classList.remove('active');

            gsap.to(serviceDetails, {
                opacity: 0,
                scale: 0.8,
                y: 20,
                duration: 0.2,
                ease: "power2.inOut",
                onComplete: () => {
                    gsap.set(serviceDetails, {
                        display: 'none',
                        visibility: 'hidden'
                    });
                }
            });
        });

        // No animation resume here
    }
    
    addCloseButton(serviceDetails, node) {
        // Remove existing close button if any
        const existingCloseBtn = serviceDetails.querySelector('.close-service-card');
        if (existingCloseBtn) {
            existingCloseBtn.remove();
        }
        
        // Create close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-service-card';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            position: absolute;
            top: 5px;
            right: 5px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        `;
        
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
        });
        
        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.background = 'none';
        });
        
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeServiceCard(node);
        });
        
        serviceDetails.appendChild(closeBtn);
    }
    
    closeServiceCard(node) {
        const serviceDetails = node.querySelector('.service-details-mini');
        node.classList.remove('active');

        // Set flag immediately to allow animation to resume
        this.isCardOpen = false;
        console.log('Single card closing - isCardOpen set to:', this.isCardOpen);

        gsap.to(serviceDetails, {
            opacity: 0,
            scale: 0.8,
            y: 20,
            duration: 0.3,
            ease: "power2.inOut",
            onComplete: () => {
                gsap.set(serviceDetails, {
                    display: 'none',
                    visibility: 'hidden'
                });
                this.resumeAnimation();
            }
        });
    }
    
    focusOnNode(node, index) {
        // Only focus if card is open (no rotation during card view)
        if (this.isCardOpen) {
            // Calculate the angle to bring this node to the top (12 o'clock position)
            const targetAngle = -index * 60 + 90; // Each node is 60 degrees apart, +90 to bring to top

            // Update current rotation to match the target
            this.currentRotation = targetAngle;

            // Animate the orbital ring to the target position
            gsap.to(this.orbitalRing, {
                rotation: targetAngle,
                duration: 0.8,
                ease: "power2.inOut"
            });

            // Update all service nodes positions to match the new rotation
            this.serviceNodes.forEach((serviceNode, nodeIndex) => {
                const baseAngle = nodeIndex * 60;
                const orbitAngle = targetAngle;

                const x = Math.cos((baseAngle + orbitAngle) * Math.PI / 180) * 150;
                const y = Math.sin((baseAngle + orbitAngle) * Math.PI / 180) * 150;

                gsap.to(serviceNode, {
                    x: x,
                    y: y,
                    duration: 0.8,
                    ease: "power2.inOut"
                });
            });
        }
    }
    
    startAnimation() {
        // Cancel any existing animation frame first
        this.cancelAnimation();
        
        // Double check before starting
        if (!this.isAnimating || this.isCardOpen) {
            console.log('Animation blocked - isAnimating:', this.isAnimating, 'isCardOpen:', this.isCardOpen);
            return;
        }
        
        // Animation loop function
        const animate = () => {
            // Check again inside the loop
            if (!this.isAnimating || this.isCardOpen) {
                console.log('Animation loop stopped - isAnimating:', this.isAnimating, 'isCardOpen:', this.isCardOpen);
                this.animationFrameId = null;
                return;
            }
            
            this.currentRotation += this.animationSpeed;
            
            // Rotate the orbital ring
            if (this.orbitalRing) {
                this.orbitalRing.style.transform = `translate(-50%, -50%) rotate(${this.currentRotation}deg)`;
            }
            
            // Update orbital positions but keep text stationary
            this.serviceNodes.forEach((node, index) => {
                const baseAngle = index * 60;
                const orbitAngle = this.currentRotation;
                
                // Update the orbital position
                const x = Math.cos((baseAngle + orbitAngle) * Math.PI / 180) * 150;
                const y = Math.sin((baseAngle + orbitAngle) * Math.PI / 180) * 150;
                
                node.style.transform = `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`;
            });
            
            // Continue animation if conditions are still met
            if (this.isAnimating && !this.isCardOpen) {
                this.animationFrameId = requestAnimationFrame(animate);
            } else {
                console.log('Animation stopped in loop');
                this.animationFrameId = null;
            }
        };
        
        // Start the animation
        this.animationFrameId = requestAnimationFrame(animate);
        console.log('Animation started with frame ID:', this.animationFrameId);
    }
    
    cancelAnimation() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
            console.log('Animation frame cancelled and cleared');
        }
    }
    
    pauseAnimation() {
        this.isAnimating = false;
        this.cancelAnimation();
        console.log('Animation paused - isAnimating:', this.isAnimating);
    }
    
    resumeAnimation() {
        if (!this.isCardOpen) {
            this.isAnimating = true;
            console.log('Resuming animation - isCardOpen:', this.isCardOpen, 'isAnimating:', this.isAnimating);
            this.startAnimation();
        } else {
            console.log('Animation resume blocked - card is still open');
        }
    }
    
    animateOnScroll() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateEntrance();
                }
            });
        }, {
            threshold: 0.3
        });
        
        if (this.container) {
            observer.observe(this.container);
        }
    }
    
    animateEntrance() {
        // Animate central hub
        gsap.fromTo(this.centralHub, 
            { scale: 0, opacity: 0 },
            { 
                scale: 1, 
                opacity: 1, 
                duration: 0.8, 
                ease: "back.out(1.7)",
                delay: 0.2 
            }
        );
        
        // Animate orbital ring
        gsap.fromTo(this.orbitalRing,
            { scale: 0, opacity: 0 },
            { 
                scale: 1, 
                opacity: 1, 
                duration: 1, 
                ease: "power2.out",
                delay: 0.4 
            }
        );
        
        // Animate service nodes with stagger
        gsap.fromTo(this.serviceNodes,
            { scale: 0, opacity: 0 },
            { 
                scale: 1, 
                opacity: 1, 
                duration: 0.6, 
                ease: "back.out(1.7)",
                stagger: 0.1,
                delay: 0.6 
            }
        );
    }
    
    observeEntrance() {
        const header = document.querySelector('.services-header-mini');
        if (header) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        gsap.to(header, {
                            opacity: 1,
                            y: 0,
                            duration: 0.8,
                            ease: "power2.out"
                        });
                    }
                });
            }, {
                threshold: 0.5
            });
            
            observer.observe(header);
        }
    }
}

// Enhanced Service Node Interactions
class ServiceNodeEnhancer {
    constructor() {
        this.nodes = document.querySelectorAll('.service-node-mini');
        this.init();
    }
    
    init() {
        this.nodes.forEach((node, index) => {
            this.enhanceNode(node, index);
        });
    }
    
    enhanceNode(node, index) {
        const icon = node.querySelector('.node-icon-mini');
        const title = node.querySelector('.node-title-mini');
        
        // Add micro-interactions
        node.addEventListener('mouseenter', () => {
            // Icon bounce
            gsap.to(icon, {
                y: -5,
                duration: 0.3,
                ease: "power2.out"
            });
            
            // Title glow
            gsap.to(title, {
                textShadow: "0 0 10px rgba(255, 255, 255, 0.8)",
                duration: 0.3
            });
        });
        
        node.addEventListener('mouseleave', () => {
            // Reset icon
            gsap.to(icon, {
                y: 0,
                duration: 0.3,
                ease: "power2.out"
            });
            
            // Reset title
            gsap.to(title, {
                textShadow: "0 1px 3px rgba(0, 0, 0, 0.3)",
                duration: 0.3
            });
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for GSAP to load
    if (typeof gsap !== 'undefined') {
        new ServicesOrbitalTimeline();
        new ServiceNodeEnhancer();
    } else {
        console.warn('GSAP not loaded. Orbital timeline animations will not work.');
    }
});

// Performance optimization: Pause animations when tab is not visible
document.addEventListener('visibilitychange', () => {
    const timeline = window.servicesOrbitalTimeline;
    if (timeline) {
        if (document.hidden) {
            timeline.pauseAnimation();
        } else {
            timeline.resumeAnimation();
        }
    }
}); 