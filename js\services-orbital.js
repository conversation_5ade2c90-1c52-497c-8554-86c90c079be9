// Services Orbital Timeline Animation
class ServicesOrbitalTimeline {
    constructor() {
        this.container = document.getElementById('orbitalContainerMini');
        this.serviceNodes = document.querySelectorAll('.service-node-mini');
        this.centralHub = document.querySelector('.central-hub-mini');
        this.orbitalRing = document.querySelector('.orbital-ring-mini');
        
        this.isAnimating = true;
        this.rotationAngle = 0; // degrees
        this.rotationSpeed = 0.3; // deg per 50ms (6 deg/sec)
        this.autoRotate = true;
        this.isCardOpen = false;
        this.animationFrameId = null;
        this.focusedNodeIndex = null;
        
        this.init();
    }
    
    init() {
        if (!this.container) return;
        
        this.setupEventListeners();
        this.startAnimation();
        this.animateOnScroll();
    }
    
    setupEventListeners() {
        // Service node hover events
        this.serviceNodes.forEach((node, index) => {
            node.addEventListener('mouseenter', () => this.onNodeHover(node, index));
            node.addEventListener('mouseleave', () => this.onNodeLeave(node, index));
            node.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent event bubbling
                this.onNodeClick(node, index);
            });
        });
        
        // Container pause on hover (only if no card is open)
        this.container.addEventListener('mouseenter', () => {
            if (!this.isCardOpen) {
                this.pauseAnimation();
            }
        });
        
        this.container.addEventListener('mouseleave', () => {
            if (!this.isCardOpen) {
                this.resumeAnimation();
            }
        });
        
        // Close cards when clicking outside the container
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target) && this.isCardOpen) {
                this.closeAllServiceCards();
            }
        });
        
        // Also close cards when clicking on the container but not on nodes
        this.container.addEventListener('click', (e) => {
            if (e.target === this.container && this.isCardOpen) {
                this.closeAllServiceCards();
            }
        });
        
        // Intersection Observer for entrance animation
        this.observeEntrance();
    }
    
    onNodeHover(node, index) {
        this.focusedNodeIndex = index;
        
        // Scale up the hovered node
        gsap.to(node.querySelector('.node-icon-mini'), {
            scale: 1.3,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
        
        // Pulse effect
        gsap.to(node.querySelector('.node-icon-mini'), {
            boxShadow: "0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(185, 34, 75, 0.6)",
            duration: 0.3
        });
        
        // Highlight related nodes (for demo purposes, we'll highlight adjacent nodes)
        this.highlightRelatedNodes(index);
    }
    
    onNodeLeave(node, index) {
        this.focusedNodeIndex = null;
        
        // Reset scale
        gsap.to(node.querySelector('.node-icon-mini'), {
            scale: 1,
            duration: 0.3,
            ease: "power2.out"
        });
        
        // Reset shadow
        gsap.to(node.querySelector('.node-icon-mini'), {
            boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
            duration: 0.3
        });
        
        // Remove highlights
        this.removeHighlights();
    }
    
    onNodeClick(node, index) {
        // Check if this node is already active
        if (node.classList.contains('active')) {
            // If already active, close it
            this.closeServiceCard(node);
            return;
        }

        this.autoRotate = false;
        this.isCardOpen = true;
        this.focusedNodeIndex = index;

        // Stop CSS animations
        this.orbitalRing.style.animationPlayState = 'paused';
        this.serviceNodes.forEach(serviceNode => {
            serviceNode.style.animationPlayState = 'paused';
        });

        this.pauseAnimation();
        this.closeExistingCards();

        // Move clicked node to 12 o'clock position (top)
        const targetAngle = -index * 60; // Each node is 60 degrees apart
        gsap.to(this, {
            rotationAngle: targetAngle,
            duration: 0.5,
            ease: "power2.out",
            onUpdate: () => this.renderNodes(),
            onComplete: () => {
                this.showServiceCard(node, index);
            }
        });
    }
    
    highlightRelatedNodes(currentIndex) {
        this.serviceNodes.forEach((node, index) => {
            const icon = node.querySelector('.node-icon-mini');
            if (index !== currentIndex) {
                // Adjacent nodes get subtle highlight
                const isAdjacent = Math.abs(index - currentIndex) === 1 || 
                                 (currentIndex === 0 && index === this.serviceNodes.length - 1) || 
                                 (currentIndex === this.serviceNodes.length - 1 && index === 0);
                
                if (isAdjacent) {
                    gsap.to(icon, {
                        scale: 1.1,
                        boxShadow: "0 0 10px rgba(255, 255, 255, 0.4)",
                        duration: 0.3
                    });
                } else {
                    gsap.to(icon, {
                        opacity: 0.6,
                        duration: 0.3
                    });
                }
            }
        });
    }
    
    removeHighlights() {
        this.serviceNodes.forEach((node) => {
            const icon = node.querySelector('.node-icon-mini');
            gsap.to(icon, {
                scale: 1,
                opacity: 1,
                boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
                duration: 0.3
            });
        });
    }
    
    createRippleEffect(node) {
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 5;
            transform: translate(-50%, -50%);
        `;
        
        node.appendChild(ripple);
        
        gsap.to(ripple, {
            width: 100,
            height: 100,
            opacity: 0,
            duration: 0.6,
            ease: "power2.out",
            onComplete: () => ripple.remove()
        });
    }
    
    showServiceCard(node, index) {
        const serviceDetails = node.querySelector('.service-details-mini');
        node.classList.add('active');

        // Show the service card instantly with no blur
        gsap.set(serviceDetails, {
            display: 'block',
            visibility: 'visible',
            opacity: 1,
            scale: 1
        });

        // Add close button functionality
        this.addCloseButton(serviceDetails, node);
    }
    
    closeAllServiceCards() {
        this.isCardOpen = false;
        this.autoRotate = true;
        this.focusedNodeIndex = null;
        this.serviceNodes.forEach(node => {
            const serviceDetails = node.querySelector('.service-details-mini');
            node.classList.remove('active');

            gsap.to(serviceDetails, {
                opacity: 0,
                scale: 0.8,
                duration: 0.3,
                ease: "power2.inOut",
                onComplete: () => {
                    gsap.set(serviceDetails, { display: 'none' });
                }
            });
        });

        // Resume CSS animations
        this.orbitalRing.style.animationPlayState = 'running';
        this.serviceNodes.forEach(serviceNode => {
            serviceNode.style.animationPlayState = 'running';
        });

        // Resume animation immediately
        this.resumeAnimation();
    }
    
    closeExistingCards() {
        // Close cards without resuming animation (for when opening new card)
        this.serviceNodes.forEach(node => {
            const serviceDetails = node.querySelector('.service-details-mini');
            node.classList.remove('active');

            gsap.to(serviceDetails, {
                opacity: 0,
                scale: 0.8,
                duration: 0.2,
                ease: "power2.inOut",
                onComplete: () => {
                    gsap.set(serviceDetails, { display: 'none' });
                }
            });
        });

        // No animation resume here
    }
    
    addCloseButton(serviceDetails, node) {
        // Remove existing close button if any
        const existingCloseBtn = serviceDetails.querySelector('.close-service-card');
        if (existingCloseBtn) {
            existingCloseBtn.remove();
        }
        
        // Create close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-service-card';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            position: absolute;
            top: 5px;
            right: 5px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        `;
        
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
        });
        
        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.background = 'none';
        });
        
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeServiceCard(node);
        });
        
        serviceDetails.appendChild(closeBtn);
    }
    
    closeServiceCard(node) {
        const serviceDetails = node.querySelector('.service-details-mini');
        node.classList.remove('active');

        // Set flag immediately to allow animation to resume
        this.isCardOpen = false;
        this.autoRotate = true;
        console.log('Single card closing - isCardOpen set to:', this.isCardOpen);

        gsap.to(serviceDetails, {
            opacity: 0,
            scale: 0.8,
            duration: 0.3,
            ease: "power2.inOut",
            onComplete: () => {
                gsap.set(serviceDetails, { display: 'none' });

                // Resume CSS animations
                this.orbitalRing.style.animationPlayState = 'running';
                this.serviceNodes.forEach(serviceNode => {
                    serviceNode.style.animationPlayState = 'running';
                });

                this.resumeAnimation();
            }
        });
    }
    
    focusNodeToTop(index, cb) {
        const total = this.serviceNodes.length;
        // Calculate angle to bring clicked node to 12 o'clock (top) position
        // Node 0 should be at top (270 degrees), so we calculate from there
        const targetAngle = 270 - (index * 60); // Each node is 60 degrees apart
        gsap.to(this, {
            rotationAngle: targetAngle,
            duration: 0.6,
            ease: "power2.inOut",
            onUpdate: () => this.renderNodes(),
            onComplete: cb
        });
    }
    
    startAnimation() {
        this.cancelAnimation();
        const animate = () => {
            if (this.autoRotate && !this.isCardOpen) {
                this.rotationAngle = (this.rotationAngle + this.rotationSpeed) % 360;
            }
            this.renderNodes();
            if (this.autoRotate && !this.isCardOpen) {
                this.animationFrameId = requestAnimationFrame(animate);
            }
        };
        this.animationFrameId = requestAnimationFrame(animate);
    }
    
    renderNodes() {
        const total = this.serviceNodes.length;
        this.serviceNodes.forEach((node, index) => {
            const angle = ((index / total) * 360 + this.rotationAngle) % 360;
            const radius = 150;
            const radian = (angle * Math.PI) / 180;
            const x = radius * Math.cos(radian);
            const y = radius * Math.sin(radian);
            node.style.transform = `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`;
            node.style.zIndex = Math.round(100 + 50 * Math.cos(radian));
            node.style.opacity = this.isCardOpen && this.focusedNodeIndex !== index ? 0.4 : 1;
        });
        if (this.orbitalRing) {
            this.orbitalRing.style.transform = `translate(-50%, -50%) rotate(${this.rotationAngle}deg)`;
        }
    }
    
    cancelAnimation() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
            console.log('Animation frame cancelled and cleared');
        }
    }
    
    pauseAnimation() {
        this.isAnimating = false;
        this.cancelAnimation();
        console.log('Animation paused - isAnimating:', this.isAnimating);
    }
    
    resumeAnimation() {
        if (!this.isCardOpen) {
            this.autoRotate = true;
            console.log('Resuming animation - isCardOpen:', this.isCardOpen, 'isAnimating:', this.isAnimating);
            this.startAnimation();
        } else {
            console.log('Animation resume blocked - card is still open');
        }
    }
    
    animateOnScroll() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateEntrance();
                }
            });
        }, {
            threshold: 0.3
        });
        
        if (this.container) {
            observer.observe(this.container);
        }
    }
    
    animateEntrance() {
        // Animate central hub
        gsap.fromTo(this.centralHub, 
            { scale: 0, opacity: 0 },
            { 
                scale: 1, 
                opacity: 1, 
                duration: 0.8, 
                ease: "back.out(1.7)",
                delay: 0.2 
            }
        );
        
        // Animate orbital ring
        gsap.fromTo(this.orbitalRing,
            { scale: 0, opacity: 0 },
            { 
                scale: 1, 
                opacity: 1, 
                duration: 1, 
                ease: "power2.out",
                delay: 0.4 
            }
        );
        
        // Animate service nodes with stagger
        gsap.fromTo(this.serviceNodes,
            { scale: 0, opacity: 0 },
            { 
                scale: 1, 
                opacity: 1, 
                duration: 0.6, 
                ease: "back.out(1.7)",
                stagger: 0.1,
                delay: 0.6 
            }
        );
    }
    
    observeEntrance() {
        const header = document.querySelector('.services-header-mini');
        if (header) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        gsap.to(header, {
                            opacity: 1,
                            y: 0,
                            duration: 0.8,
                            ease: "power2.out"
                        });
                    }
                });
            }, {
                threshold: 0.5
            });
            
            observer.observe(header);
        }
    }
}

// Enhanced Service Node Interactions
class ServiceNodeEnhancer {
    constructor() {
        this.nodes = document.querySelectorAll('.service-node-mini');
        this.init();
    }
    
    init() {
        this.nodes.forEach((node, index) => {
            this.enhanceNode(node, index);
        });
    }
    
    enhanceNode(node, index) {
        const icon = node.querySelector('.node-icon-mini');
        const title = node.querySelector('.node-title-mini');
        
        // Add micro-interactions
        node.addEventListener('mouseenter', () => {
            // Icon bounce
            gsap.to(icon, {
                y: -5,
                duration: 0.3,
                ease: "power2.out"
            });
            
            // Title glow
            gsap.to(title, {
                textShadow: "0 0 10px rgba(255, 255, 255, 0.8)",
                duration: 0.3
            });
        });
        
        node.addEventListener('mouseleave', () => {
            // Reset icon
            gsap.to(icon, {
                y: 0,
                duration: 0.3,
                ease: "power2.out"
            });
            
            // Reset title
            gsap.to(title, {
                textShadow: "0 1px 3px rgba(0, 0, 0, 0.3)",
                duration: 0.3
            });
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for GSAP to load
    if (typeof gsap !== 'undefined') {
        new ServicesOrbitalTimeline();
        new ServiceNodeEnhancer();
    } else {
        console.warn('GSAP not loaded. Orbital timeline animations will not work.');
    }
});

// Performance optimization: Pause animations when tab is not visible
document.addEventListener('visibilitychange', () => {
    const timeline = window.servicesOrbitalTimeline;
    if (timeline) {
        if (document.hidden) {
            timeline.pauseAnimation();
        } else {
            timeline.resumeAnimation();
        }
    }
}); 